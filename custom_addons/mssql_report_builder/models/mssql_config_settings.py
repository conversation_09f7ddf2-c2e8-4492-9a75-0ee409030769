from odoo import models, fields, api

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    mssql_host = fields.Char(string='MSSQL Server Host')
    mssql_port = fields.Char(string='MSSQL Server Port', default='1433')
    mssql_database = fields.Char(string='MSSQL Database')
    mssql_user = fields.Char(string='MSSQL User')
    mssql_password = fields.Char(string='MSSQL Password')

    def set_values(self):
        super().set_values()
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_host', self.mssql_host or '')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_port', self.mssql_port or '')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_database', self.mssql_database or '')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_user', self.mssql_user or '')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_password', self.mssql_password or '')

    @api.model
    def get_values(self):
        res = super().get_values()
        IrConfig = self.env['ir.config_parameter'].sudo()
        res.update(
            mssql_host=IrConfig.get_param('mssql_report_builder.mssql_host', default=''),
            mssql_port=IrConfig.get_param('mssql_report_builder.mssql_port', default='1433'),
            mssql_database=IrConfig.get_param('mssql_report_builder.mssql_database', default=''),
            mssql_user=IrConfig.get_param('mssql_report_builder.mssql_user', default=''),
            mssql_password=IrConfig.get_param('mssql_report_builder.mssql_password', default=''),
        )
        return res
