import logging
from odoo import models, api
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class MssqlConnection(models.AbstractModel):
    _name = 'mssql.connection'
    _description = 'MSSQL Connection Helper'

    @api.model
    def get_connection(self, connection_id=None):
        """Get MSSQL connection with proper error handling"""
        try:
            if connection_id:
                # Use specific connection
                connection_config = self.env['mssql.connection.config'].browse(connection_id)
                if not connection_config.exists():
                    raise UserError(f'Connection configuration with ID {connection_id} not found.')
            else:
                # Use default connection
                connection_config = self.env['mssql.connection.config'].get_default_connection()

            if not connection_config.active:
                raise UserError(f'Connection "{connection_config.name}" is not active.')

            return connection_config.get_connection()

        except UserError:
            # Re-raise UserError as-is
            raise
        except Exception as e:
            _logger.error(f'Unexpected error in MSSQL connection helper: {str(e)}')
            raise UserError(f'Unexpected error: {str(e)}')
