import pyodbc
import logging
from odoo import models, api
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class MssqlConnection(models.AbstractModel):
    _name = 'mssql.connection'
    _description = 'MSSQL Connection Helper'

    @api.model
    def get_connection(self):
        """Get MSSQL connection with proper error handling"""
        try:
            IrConfig = self.env['ir.config_parameter'].sudo()
            host = IrConfig.get_param('mssql_report_builder.mssql_host')
            port = IrConfig.get_param('mssql_report_builder.mssql_port', '1433')
            database = IrConfig.get_param('mssql_report_builder.mssql_database')
            user = IrConfig.get_param('mssql_report_builder.mssql_user')
            password = IrConfig.get_param('mssql_report_builder.mssql_password')

            if not (host and database and user and password):
                raise UserError('MSSQL connection configuration is incomplete. Please check Settings.')

            conn_str = (
                f'DRIVER={{ODBC Driver 17 for SQL Server}};'
                f'SERVER={host},{port};'
                f'DATABASE={database};'
                f'UID={user};PWD={password};'
                f'Connection Timeout=30;'
            )

            connection = pyodbc.connect(conn_str)
            _logger.info('MSSQL connection established successfully')
            return connection

        except pyodbc.Error as e:
            _logger.error(f'MSSQL connection error: {str(e)}')
            raise UserError(f'Cannot connect to MSSQL Server: {str(e)}')
        except Exception as e:
            _logger.error(f'Unexpected error in MSSQL connection: {str(e)}')
            raise UserError(f'Unexpected error: {str(e)}')
