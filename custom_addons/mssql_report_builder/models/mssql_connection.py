import pyodbc
from odoo import models, api

class MssqlConnection(models.AbstractModel):
    _name = 'mssql.connection'
    _description = 'MSSQL Connection Helper'

    @api.model
    def get_connection(self):
        IrConfig = self.env['ir.config_parameter'].sudo()
        host = IrConfig.get_param('mssql_report_builder.mssql_host')
        port = IrConfig.get_param('mssql_report_builder.mssql_port', '1433')
        database = IrConfig.get_param('mssql_report_builder.mssql_database')
        user = IrConfig.get_param('mssql_report_builder.mssql_user')
        password = IrConfig.get_param('mssql_report_builder.mssql_password')
        if not (host and database and user and password):
            raise Exception('MSSQL connection info is incomplete!')
        conn_str = (
            f'DRIVER={{ODBC Driver 17 for SQL Server}};'
            f'SERVER={host},{port};'
            f'DATABASE={database};'
            f'UID={user};PWD={password}'
        )
        return pyodbc.connect(conn_str)
