from odoo import models, fields, api

class MssqlReport(models.Model):
    _name = 'mssql.report'
    _description = 'MSSQL Report'

    name = fields.Char('Report Name', required=True)
    report_type = fields.Selection([
        ('table', 'Table'),
        ('pivot', 'Pivot'),
    ], string='Report Type', required=True, default='table')
    source_type = fields.Selection([
        ('view', 'View'),
        ('procedure', 'Stored Procedure'),
    ], string='Source Type', required=True, default='view')
    source_name = fields.Char('View/Procedure Name', required=True)
    parameter_ids = fields.One2many('mssql.report.parameter', 'report_id', string='Parameters')

    @api.model
    def get_report_data(self, params=None):
        params = params or {}
        connection = self.env['mssql.connection'].get_connection()
        cursor = connection.cursor()
        data = []
        if self.source_type == 'view':
            sql = f"SELECT * FROM {self.source_name}"
            cursor.execute(sql)
            columns = [column[0] for column in cursor.description]
            for row in cursor.fetchall():
                data.append(dict(zip(columns, row)))
        elif self.source_type == 'procedure':
            # Xây dựng câu lệnh EXEC với tham số
            param_str = ', '.join([f"@{k}=?" for k in params.keys()])
            sql = f"EXEC {self.source_name} {param_str}"
            cursor.execute(sql, list(params.values()))
            columns = [column[0] for column in cursor.description]
            for row in cursor.fetchall():
                data.append(dict(zip(columns, row)))
        cursor.close()
        connection.close()
        return data

class MssqlReportParameter(models.Model):
    _name = 'mssql.report.parameter'
    _description = 'MSSQL Report Parameter'

    name = fields.Char('Parameter Name', required=True)
    param_type = fields.Selection([
        ('char', 'String'),
        ('int', 'Integer'),
        ('date', 'Date'),
        ('datetime', 'Datetime'),
    ], string='Type', required=True, default='char')
    default_value = fields.Char('Default Value')
    report_id = fields.Many2one('mssql.report', string='Report', ondelete='cascade')
