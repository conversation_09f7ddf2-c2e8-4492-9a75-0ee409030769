import logging
from odoo import models, fields
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class MssqlReport(models.Model):
    _name = 'mssql.report'
    _description = 'MSSQL Report'
    _rec_name = 'name'

    name = fields.Char('Report Name', required=True)
    report_type = fields.Selection([
        ('table', 'Table'),
        ('pivot', 'Pivot'),
    ], string='Report Type', required=True, default='table')
    source_type = fields.Selection([
        ('view', 'View'),
        ('procedure', 'Stored Procedure'),
    ], string='Source Type', required=True, default='view')
    source_name = fields.Char('View/Procedure Name', required=True)
    connection_id = fields.Many2one('mssql.connection.config', string='Connection',
                                   help='MSSQL connection to use. If empty, default connection will be used.')
    parameter_ids = fields.One2many('mssql.report.parameter', 'report_id', string='Parameters')
    active = fields.Boolean('Active', default=True)

    def get_report_data(self, params=None):
        """Get report data with proper error handling and connection management"""
        params = params or {}
        connection = None
        cursor = None

        try:
            connection = self.env['mssql.connection'].get_connection(self.connection_id.id if self.connection_id else None)
            cursor = connection.cursor()
            data = []
            columns = []

            if self.source_type == 'view':
                sql = f"SELECT * FROM {self.source_name}"
                _logger.info(f'Executing view query: {sql}')
                cursor.execute(sql)

            elif self.source_type == 'procedure':
                # Validate parameters
                if params:
                    param_str = ', '.join([f"@{k}=?" for k in params.keys()])
                    sql = f"EXEC {self.source_name} {param_str}"
                    param_values = list(params.values())
                    _logger.info(f'Executing procedure: {sql} with params: {param_values}')
                    cursor.execute(sql, param_values)
                else:
                    sql = f"EXEC {self.source_name}"
                    _logger.info(f'Executing procedure: {sql}')
                    cursor.execute(sql)

            # Get column names
            if cursor.description:
                columns = [column[0] for column in cursor.description]

                # Fetch all rows
                for row in cursor.fetchall():
                    # Convert row to dict, handling None values and data types
                    row_dict = {}
                    for i, value in enumerate(row):
                        if value is not None:
                            # Convert datetime objects to string for JSON serialization
                            if hasattr(value, 'strftime'):
                                row_dict[columns[i]] = value.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                row_dict[columns[i]] = value
                        else:
                            row_dict[columns[i]] = ''
                    data.append(row_dict)

            _logger.info(f'Retrieved {len(data)} rows from {self.source_name}')
            return {'data': data, 'columns': columns}

        except Exception as e:
            _logger.error(f'Error executing report {self.name}: {str(e)}')
            raise UserError(f'Error executing report: {str(e)}')

        finally:
            # Ensure proper cleanup
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def action_run_report(self):
        """Open wizard to run report with parameters"""
        wizard = self.env['mssql.report.wizard'].create({
            'report_id': self.id,
        })

        return {
            'type': 'ir.actions.act_window',
            'name': f'Run Report: {self.name}',
            'res_model': 'mssql.report.wizard',
            'res_id': wizard.id,
            'view_mode': 'form',
            'target': 'new',
        }

class MssqlReportParameter(models.Model):
    _name = 'mssql.report.parameter'
    _description = 'MSSQL Report Parameter'

    name = fields.Char('Parameter Name', required=True)
    param_type = fields.Selection([
        ('char', 'String'),
        ('int', 'Integer'),
        ('date', 'Date'),
        ('datetime', 'Datetime'),
    ], string='Type', required=True, default='char')
    default_value = fields.Char('Default Value')
    report_id = fields.Many2one('mssql.report', string='Report', ondelete='cascade')
