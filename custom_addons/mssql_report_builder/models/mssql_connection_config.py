import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import pyodbc

_logger = logging.getLogger(__name__)


class MssqlConnectionConfig(models.Model):
    _name = 'mssql.connection.config'
    _description = 'MSSQL Connection Configuration'
    _rec_name = 'name'

    name = fields.Char('Connection Name', required=True, default='Default MSSQL Connection')
    host = fields.Char('Server Host', required=True, help='IP address or hostname of SQL Server')
    port = fields.Char('Server Port', default='1433', help='Port number (default: 1433)')
    database = fields.Char('Database Name', required=True, help='Name of the database to connect to')
    username = fields.Char('Username', required=True, help='Username for database connection')
    password = fields.Char('Password', required=True, help='Password for database connection')
    
    # Connection settings
    connection_timeout = fields.Integer('Connection Timeout (seconds)', default=30, 
                                      help='Timeout for establishing connection')
    active = fields.Bo<PERSON>an('Active', default=True)
    is_default = fields.Boolean('Default Connection', default=False,
                               help='Use this connection as default for reports')
    
    # Status fields
    last_test_date = fields.Datetime('Last Test Date', readonly=True)
    last_test_result = fields.Selection([
        ('success', 'Success'),
        ('failed', 'Failed')
    ], string='Last Test Result', readonly=True)
    last_test_message = fields.Text('Last Test Message', readonly=True)
    
    # Additional connection parameters
    driver = fields.Selection([
        ('ODBC Driver 17 for SQL Server', 'ODBC Driver 17 for SQL Server'),
        ('ODBC Driver 18 for SQL Server', 'ODBC Driver 18 for SQL Server'),
        ('SQL Server', 'SQL Server (Legacy)'),
    ], string='ODBC Driver', default='ODBC Driver 17 for SQL Server', required=True)
    
    encrypt = fields.Boolean('Encrypt Connection', default=False,
                            help='Use encrypted connection (TLS/SSL)')
    trust_server_certificate = fields.Boolean('Trust Server Certificate', default=False,
                                            help='Trust server certificate without validation')

    @api.model
    def create(self, vals):
        # Ensure only one default connection
        if vals.get('is_default'):
            self.search([('is_default', '=', True)]).write({'is_default': False})
        return super().create(vals)

    def write(self, vals):
        # Ensure only one default connection
        if vals.get('is_default'):
            self.search([('is_default', '=', True), ('id', '!=', self.id)]).write({'is_default': False})
        return super().write(vals)

    def get_connection_string(self):
        """Build connection string from configuration"""
        self.ensure_one()
        
        conn_params = [
            f'DRIVER={{{self.driver}}}',
            f'SERVER={self.host},{self.port}',
            f'DATABASE={self.database}',
            f'UID={self.username}',
            f'PWD={self.password}',
            f'Connection Timeout={self.connection_timeout}',
        ]
        
        if self.encrypt:
            conn_params.append('Encrypt=yes')
        
        if self.trust_server_certificate:
            conn_params.append('TrustServerCertificate=yes')
            
        return ';'.join(conn_params)

    def test_connection(self):
        """Test database connection and update status"""
        self.ensure_one()
        
        try:
            conn_str = self.get_connection_string()
            _logger.info(f'Testing MSSQL connection: {self.name}')
            
            # Attempt to connect
            connection = pyodbc.connect(conn_str)
            cursor = connection.cursor()
            
            # Test with a simple query
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            # Update status
            self.write({
                'last_test_date': fields.Datetime.now(),
                'last_test_result': 'success',
                'last_test_message': 'Connection successful!'
            })
            
            _logger.info(f'MSSQL connection test successful: {self.name}')
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Test'),
                    'message': _('Connection successful!'),
                    'type': 'success',
                    'sticky': False,
                }
            }
            
        except pyodbc.Error as e:
            error_msg = f'ODBC Error: {str(e)}'
            _logger.error(f'MSSQL connection test failed: {self.name} - {error_msg}')
            
            self.write({
                'last_test_date': fields.Datetime.now(),
                'last_test_result': 'failed',
                'last_test_message': error_msg
            })
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Test Failed'),
                    'message': error_msg,
                    'type': 'danger',
                    'sticky': True,
                }
            }
            
        except Exception as e:
            error_msg = f'Unexpected error: {str(e)}'
            _logger.error(f'MSSQL connection test failed: {self.name} - {error_msg}')
            
            self.write({
                'last_test_date': fields.Datetime.now(),
                'last_test_result': 'failed',
                'last_test_message': error_msg
            })
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Test Failed'),
                    'message': error_msg,
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def get_connection(self):
        """Get database connection"""
        self.ensure_one()
        
        try:
            conn_str = self.get_connection_string()
            connection = pyodbc.connect(conn_str)
            _logger.info(f'MSSQL connection established: {self.name}')
            return connection
            
        except pyodbc.Error as e:
            _logger.error(f'MSSQL connection error: {self.name} - {str(e)}')
            raise UserError(f'Cannot connect to MSSQL Server "{self.name}": {str(e)}')
        except Exception as e:
            _logger.error(f'Unexpected error in MSSQL connection: {self.name} - {str(e)}')
            raise UserError(f'Unexpected error: {str(e)}')

    @api.model
    def get_default_connection(self):
        """Get default connection configuration"""
        default_conn = self.search([('is_default', '=', True), ('active', '=', True)], limit=1)
        if not default_conn:
            # If no default, get the first active connection
            default_conn = self.search([('active', '=', True)], limit=1)
        
        if not default_conn:
            raise UserError(_('No active MSSQL connection configuration found. Please configure a connection first.'))
        
        return default_conn
