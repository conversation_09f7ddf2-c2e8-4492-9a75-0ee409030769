import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError

try:
    import pyodbc
    PYODBC_AVAILABLE = True
except ImportError:
    PYODBC_AVAILABLE = False

try:
    import pymssql
    PYMSSQL_AVAILABLE = True
except ImportError:
    PYMSSQL_AVAILABLE = False

_logger = logging.getLogger(__name__)


class MssqlConnectionConfig(models.Model):
    _name = 'mssql.connection.config'
    _description = 'MSSQL Connection Configuration'
    _rec_name = 'name'

    name = fields.Char('Connection Name', required=True, default='Default MSSQL Connection')
    host = fields.Char('Server Host', required=True, help='IP address or hostname of SQL Server')
    port = fields.Char('Server Port', default='1433', help='Port number (default: 1433)')
    database = fields.Char('Database Name', required=True, help='Name of the database to connect to')
    username = fields.Char('Username', required=True, help='Username for database connection')
    password = fields.Char('Password', required=True, help='Password for database connection')
    
    # Connection settings
    connection_timeout = fields.Integer('Connection Timeout (seconds)', default=30, 
                                      help='Timeout for establishing connection')
    active = fields.Boolean('Active', default=True)
    is_default = fields.Boolean('Default Connection', default=False,
                               help='Use this connection as default for reports')
    
    # Status fields
    last_test_date = fields.Datetime('Last Test Date', readonly=True)
    last_test_result = fields.Selection([
        ('success', 'Success'),
        ('failed', 'Failed')
    ], string='Last Test Result', readonly=True)
    last_test_message = fields.Text('Last Test Message', readonly=True)
    
    # Connection method and driver
    connection_method = fields.Selection([
        ('pyodbc', 'PyODBC (requires ODBC drivers)'),
        ('pymssql', 'PyMSSQL (built-in FreeTDS)'),
    ], string='Connection Method', default='pymssql', required=True,
       help='Choose connection method. PyMSSQL is recommended if ODBC drivers are not available.')

    driver = fields.Selection([
        ('ODBC Driver 17 for SQL Server', 'ODBC Driver 17 for SQL Server'),
        ('ODBC Driver 18 for SQL Server', 'ODBC Driver 18 for SQL Server'),
        ('SQL Server', 'SQL Server (Legacy)'),
        ('FreeTDS', 'FreeTDS'),
        ('TDS', 'TDS'),
    ], string='ODBC Driver', default='FreeTDS',
       help='Only used when Connection Method is PyODBC')
    
    encrypt = fields.Boolean('Encrypt Connection', default=False,
                            help='Use encrypted connection (TLS/SSL)')
    trust_server_certificate = fields.Boolean('Trust Server Certificate', default=False,
                                            help='Trust server certificate without validation')

    available_drivers = fields.Text('Available Drivers', compute='_compute_available_drivers',
                                   help='List of available ODBC drivers on this system')

    @api.depends()
    def _compute_available_drivers(self):
        """Compute available connection methods and drivers"""
        for record in self:
            info = []

            # Check PyMSSQL availability
            if PYMSSQL_AVAILABLE:
                info.append('✓ PyMSSQL: Available (recommended)')
            else:
                info.append('✗ PyMSSQL: Not installed')

            # Check PyODBC availability
            if PYODBC_AVAILABLE:
                info.append('✓ PyODBC: Available')
                try:
                    drivers = pyodbc.drivers()
                    if drivers:
                        info.append('  Available ODBC drivers:')
                        for driver in drivers:
                            info.append(f'    - {driver}')
                    else:
                        info.append('  No ODBC drivers found')
                except Exception as e:
                    info.append(f'  Error detecting ODBC drivers: {str(e)}')
            else:
                info.append('✗ PyODBC: Not installed')

            record.available_drivers = '\n'.join(info)

    @api.model
    def create(self, vals):
        # Ensure only one default connection
        if vals.get('is_default'):
            self.search([('is_default', '=', True)]).write({'is_default': False})
        return super().create(vals)

    def write(self, vals):
        # Ensure only one default connection
        if vals.get('is_default'):
            self.search([('is_default', '=', True), ('id', '!=', self.id)]).write({'is_default': False})
        return super().write(vals)

    def get_connection_string(self):
        """Build connection string from configuration (only for PyODBC)"""
        self.ensure_one()

        if self.connection_method != 'pyodbc':
            return None

        conn_params = [
            f'DRIVER={{{self.driver}}}',
            f'SERVER={self.host},{self.port}',
            f'DATABASE={self.database}',
            f'UID={self.username}',
            f'PWD={self.password}',
            f'Connection Timeout={self.connection_timeout}',
        ]

        if self.encrypt:
            conn_params.append('Encrypt=yes')

        if self.trust_server_certificate:
            conn_params.append('TrustServerCertificate=yes')

        return ';'.join(conn_params)

    def test_connection(self):
        """Test database connection and update status"""
        self.ensure_one()

        try:
            _logger.info(f'Testing MSSQL connection: {self.name} using {self.connection_method}')

            if self.connection_method == 'pymssql':
                if not PYMSSQL_AVAILABLE:
                    raise Exception('PyMSSQL is not installed. Please install it with: pip install pymssql')

                # Use PyMSSQL
                connection = pymssql.connect(
                    server=f'{self.host}:{self.port}',
                    user=self.username,
                    password=self.password,
                    database=self.database,
                    timeout=self.connection_timeout,
                    login_timeout=self.connection_timeout
                )
                cursor = connection.cursor()

            elif self.connection_method == 'pyodbc':
                if not PYODBC_AVAILABLE:
                    raise Exception('PyODBC is not installed. Please install it with: pip install pyodbc')

                conn_str = self.get_connection_string()
                connection = pyodbc.connect(conn_str)
                cursor = connection.cursor()

            else:
                raise Exception(f'Unknown connection method: {self.connection_method}')

            # Test with a simple query
            cursor.execute("SELECT 1 as test")
            cursor.fetchone()  # Just test the query execution

            cursor.close()
            connection.close()
            
            # Update status
            self.write({
                'last_test_date': fields.Datetime.now(),
                'last_test_result': 'success',
                'last_test_message': 'Connection successful!'
            })
            
            _logger.info(f'MSSQL connection test successful: {self.name}')
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Test'),
                    'message': _('Connection successful!'),
                    'type': 'success',
                    'sticky': False,
                }
            }

        except Exception as e:
            if 'pymssql' in str(e) or 'pyodbc' in str(e):
                # Library-specific error
                error_msg = str(e)
            else:
                # Generic connection error
                error_msg = f'Connection error: {str(e)}'
            _logger.error(f'MSSQL connection test failed: {self.name} - {error_msg}')

            self.write({
                'last_test_date': fields.Datetime.now(),
                'last_test_result': 'failed',
                'last_test_message': error_msg
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Test Failed'),
                    'message': error_msg,
                    'type': 'danger',
                    'sticky': True,
                }
            }
            error_msg = f'Unexpected error: {str(e)}'
            _logger.error(f'MSSQL connection test failed: {self.name} - {error_msg}')
            
            self.write({
                'last_test_date': fields.Datetime.now(),
                'last_test_result': 'failed',
                'last_test_message': error_msg
            })
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Test Failed'),
                    'message': error_msg,
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def get_connection(self):
        """Get database connection"""
        self.ensure_one()

        try:
            if self.connection_method == 'pymssql':
                if not PYMSSQL_AVAILABLE:
                    raise UserError('PyMSSQL is not installed. Please install it with: pip install pymssql')

                connection = pymssql.connect(
                    server=f'{self.host}:{self.port}',
                    user=self.username,
                    password=self.password,
                    database=self.database,
                    timeout=self.connection_timeout,
                    login_timeout=self.connection_timeout
                )

            elif self.connection_method == 'pyodbc':
                if not PYODBC_AVAILABLE:
                    raise UserError('PyODBC is not installed. Please install it with: pip install pyodbc')

                conn_str = self.get_connection_string()
                connection = pyodbc.connect(conn_str)

            else:
                raise UserError(f'Unknown connection method: {self.connection_method}')

            _logger.info(f'MSSQL connection established: {self.name} using {self.connection_method}')
            return connection

        except UserError:
            # Re-raise UserError as-is
            raise
        except Exception as e:
            _logger.error(f'MSSQL connection error: {self.name} - {str(e)}')
            raise UserError(f'Cannot connect to MSSQL Server "{self.name}": {str(e)}')

    @api.model
    def get_default_connection(self):
        """Get default connection configuration"""
        default_conn = self.search([('is_default', '=', True), ('active', '=', True)], limit=1)
        if not default_conn:
            # If no default, get the first active connection
            default_conn = self.search([('active', '=', True)], limit=1)
        
        if not default_conn:
            raise UserError(_('No active MSSQL connection configuration found. Please configure a connection first.'))
        
        return default_conn
