# MSSQL Report Builder

Custom Addon cho Odoo Community version 18 để tạo báo cáo từ Microsoft SQL Server.

## Tính năng

- **Kết nối MSSQL Server**: C<PERSON>u hình kết nối với Microsoft SQL Server thông qua Settings
- **Hỗ trợ nhiều nguồn dữ liệu**: 
  - Views (Bảng/View)
  - Stored Procedures (với tham số)
- **Loại báo cáo**:
  - Table (Bảng)
  - Pivot (Pivot Table)
- **Quản lý tham số**: Hỗ trợ các loại tham số: String, Integer, Date, Datetime
- **Giao diện thân thiện**: Wizard để nhập filter/parameter
- **Bảo mật**: Access rights và security rules đầy đủ

## Cài đặt

### Yêu cầu hệ thống

1. **Python Dependencies**:
   ```bash
   pip install pyodbc
   ```

2. **ODBC Driver**: <PERSON><PERSON><PERSON> đặt Microsoft ODBC Driver 17 for SQL Server
   ```bash
   # Ubuntu/Debian
   curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
   curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list > /etc/apt/sources.list.d/mssql-release.list
   apt-get update
   apt-get install msodbcsql17
   ```

### Cài đặt Module

1. Copy thư mục `mssql_report_builder` vào `custom_addons`
2. Restart Odoo server
3. Vào Apps → tìm "MSSQL Report Builder" → Install

## Cấu hình

### 1. Cấu hình kết nối MSSQL

Vào **Settings → General Settings → MSSQL Server Connection**:

- **MSSQL Server Host**: Địa chỉ IP hoặc hostname của SQL Server
- **MSSQL Server Port**: Port (mặc định: 1433)
- **MSSQL Database**: Tên database
- **MSSQL User**: Username để kết nối
- **MSSQL Password**: Password

### 2. Tạo Report Definition

Vào **MSSQL Reports → Report Definitions**:

1. **Tạo report mới**:
   - **Report Name**: Tên báo cáo
   - **Report Type**: Table hoặc Pivot
   - **Source Type**: View hoặc Stored Procedure
   - **View/Procedure Name**: Tên view hoặc stored procedure

2. **Thêm Parameters** (nếu sử dụng Stored Procedure):
   - **Parameter Name**: Tên tham số (không có @)
   - **Type**: String, Integer, Date, hoặc Datetime
   - **Default Value**: Giá trị mặc định

## Sử dụng

### Chạy báo cáo

1. **Từ Report List**: Vào MSSQL Reports → Report Definitions → chọn report → "Run Report"
2. **Từ Form View**: Mở report → click "Run Report" button

### Nhập tham số

1. Wizard sẽ hiển thị các tham số đã định nghĩa
2. Nhập giá trị cho các tham số
3. Click "Xem báo cáo"

### Xem kết quả

Kết quả sẽ hiển thị trong list view với:
- **Report Name**: Tên báo cáo
- **Row Data**: Dữ liệu từng dòng (JSON format)
- **Created On**: Thời gian tạo

## Ví dụ

### 1. Report từ View

```sql
-- Tạo view trong SQL Server
CREATE VIEW vw_customers AS
SELECT 
    customer_id,
    customer_name,
    email,
    phone,
    created_date
FROM customers
WHERE active = 1;
```

**Cấu hình trong Odoo**:
- Report Name: "Customer List"
- Source Type: "View"
- View/Procedure Name: "vw_customers"

### 2. Report từ Stored Procedure

```sql
-- Tạo stored procedure trong SQL Server
CREATE PROCEDURE sp_sales_report
    @start_date DATE,
    @end_date DATE,
    @limit INT = 100
AS
BEGIN
    SELECT TOP (@limit)
        order_id,
        customer_name,
        order_date,
        total_amount
    FROM orders o
    JOIN customers c ON o.customer_id = c.customer_id
    WHERE order_date BETWEEN @start_date AND @end_date
    ORDER BY order_date DESC;
END
```

**Cấu hình trong Odoo**:
- Report Name: "Sales Report by Date Range"
- Source Type: "Stored Procedure"
- View/Procedure Name: "sp_sales_report"
- Parameters:
  - start_date (Date): 2023-01-01
  - end_date (Date): 2023-12-31
  - limit (Integer): 100

## Troubleshooting

### Lỗi kết nối

1. **"Cannot connect to MSSQL Server"**:
   - Kiểm tra thông tin kết nối
   - Đảm bảo SQL Server cho phép remote connections
   - Kiểm tra firewall

2. **"ODBC Driver not found"**:
   - Cài đặt Microsoft ODBC Driver 17 for SQL Server

### Lỗi báo cáo

1. **"Invalid object name"**:
   - Kiểm tra tên view/procedure có đúng không
   - Đảm bảo user có quyền truy cập

2. **"Parameter error"**:
   - Kiểm tra tên parameter (không có @)
   - Đảm bảo kiểu dữ liệu đúng

## Phát triển

### Chạy Tests

```bash
python3 odoo-bin -d test_db --addons-path=addons,custom_addons --test-enable --test-tags=mssql_report_builder --stop-after-init
```

### Cấu trúc thư mục

```
mssql_report_builder/
├── __init__.py
├── __manifest__.py
├── README.md
├── models/
│   ├── __init__.py
│   ├── mssql_config_settings.py
│   ├── mssql_connection.py
│   ├── mssql_report.py
│   └── mssql_report_result.py
├── wizard/
│   ├── __init__.py
│   └── mssql_report_wizard.py
├── views/
│   ├── mssql_config_settings_views.xml
│   ├── mssql_report_views.xml
│   ├── mssql_report_wizard_views.xml
│   └── mssql_report_result_views.xml
├── security/
│   └── ir.model.access.csv
├── demo/
│   └── demo_data.xml
└── tests/
    ├── __init__.py
    └── test_mssql_report_builder.py
```

## License

LGPL-3

## Tác giả

Your Company
