<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo MSSQL Report for View -->
    <record id="demo_report_view" model="mssql.report">
        <field name="name">Customer List Report</field>
        <field name="report_type">table</field>
        <field name="source_type">view</field>
        <field name="source_name">vw_customers</field>
        <field name="active">True</field>
    </record>

    <!-- Demo MSSQL Report for Stored Procedure -->
    <record id="demo_report_procedure" model="mssql.report">
        <field name="name">Sales Report by Date Range</field>
        <field name="report_type">table</field>
        <field name="source_type">procedure</field>
        <field name="source_name">sp_sales_report</field>
        <field name="active">True</field>
    </record>

    <!-- Parameters for the stored procedure report -->
    <record id="demo_param_start_date" model="mssql.report.parameter">
        <field name="name">start_date</field>
        <field name="param_type">date</field>
        <field name="default_value">2023-01-01</field>
        <field name="report_id" ref="demo_report_procedure"/>
    </record>

    <record id="demo_param_end_date" model="mssql.report.parameter">
        <field name="name">end_date</field>
        <field name="param_type">date</field>
        <field name="default_value">2023-12-31</field>
        <field name="report_id" ref="demo_report_procedure"/>
    </record>

    <record id="demo_param_limit" model="mssql.report.parameter">
        <field name="name">limit</field>
        <field name="param_type">int</field>
        <field name="default_value">100</field>
        <field name="report_id" ref="demo_report_procedure"/>
    </record>

    <!-- Demo Pivot Report -->
    <record id="demo_report_pivot" model="mssql.report">
        <field name="name">Sales Summary Pivot</field>
        <field name="report_type">pivot</field>
        <field name="source_type">view</field>
        <field name="source_name">vw_sales_summary</field>
        <field name="active">True</field>
    </record>
</odoo>
