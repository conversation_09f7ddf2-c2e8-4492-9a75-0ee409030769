import unittest
from unittest.mock import patch, MagicMock
from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError


class TestMssqlReportBuilder(TransactionCase):

    def setUp(self):
        super().setUp()
        self.report_model = self.env['mssql.report']
        self.wizard_model = self.env['mssql.report.wizard']
        self.connection_model = self.env['mssql.connection']

    def test_create_report(self):
        """Test creating a basic MSSQL report"""
        report = self.report_model.create({
            'name': 'Test Report',
            'report_type': 'table',
            'source_type': 'view',
            'source_name': 'test_view',
        })
        
        self.assertEqual(report.name, 'Test Report')
        self.assertEqual(report.report_type, 'table')
        self.assertEqual(report.source_type, 'view')
        self.assertEqual(report.source_name, 'test_view')
        self.assertTrue(report.active)

    def test_create_report_with_parameters(self):
        """Test creating a report with parameters"""
        report = self.report_model.create({
            'name': 'Test Report with Params',
            'report_type': 'table',
            'source_type': 'procedure',
            'source_name': 'test_procedure',
        })
        
        # Add parameters
        param1 = self.env['mssql.report.parameter'].create({
            'name': 'start_date',
            'param_type': 'date',
            'default_value': '2023-01-01',
            'report_id': report.id,
        })
        
        param2 = self.env['mssql.report.parameter'].create({
            'name': 'limit',
            'param_type': 'int',
            'default_value': '100',
            'report_id': report.id,
        })
        
        self.assertEqual(len(report.parameter_ids), 2)
        self.assertEqual(param1.name, 'start_date')
        self.assertEqual(param2.param_type, 'int')

    @patch('odoo.addons.mssql_report_builder.models.mssql_connection.pyodbc')
    def test_connection_success(self, mock_pyodbc):
        """Test successful MSSQL connection"""
        # Set configuration parameters
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_host', 'localhost')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_port', '1433')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_database', 'testdb')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_user', 'testuser')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_password', 'testpass')

        mock_connection = MagicMock()
        mock_pyodbc.connect.return_value = mock_connection

        connection = self.connection_model.get_connection()

        self.assertEqual(connection, mock_connection)
        mock_pyodbc.connect.assert_called_once()

    def test_connection_missing_config(self):
        """Test connection failure due to missing configuration"""
        # Clear configuration parameters
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_host', '')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_database', '')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_user', '')
        self.env['ir.config_parameter'].sudo().set_param('mssql_report_builder.mssql_password', '')

        with self.assertRaises(UserError) as context:
            self.connection_model.get_connection()

        self.assertIn('incomplete', str(context.exception))

    def test_action_run_report(self):
        """Test running report action"""
        report = self.report_model.create({
            'name': 'Test Report',
            'report_type': 'table',
            'source_type': 'view',
            'source_name': 'test_view',
        })
        
        result = report.action_run_report()
        
        self.assertEqual(result['type'], 'ir.actions.act_window')
        self.assertEqual(result['res_model'], 'mssql.report.wizard')
        self.assertEqual(result['view_mode'], 'form')
        self.assertEqual(result['target'], 'new')

    def test_wizard_onchange_report_id(self):
        """Test wizard parameter population on report selection"""
        # Create report with parameters
        report = self.report_model.create({
            'name': 'Test Report',
            'report_type': 'table',
            'source_type': 'procedure',
            'source_name': 'test_procedure',
        })
        
        param = self.env['mssql.report.parameter'].create({
            'name': 'test_param',
            'param_type': 'char',
            'default_value': 'default_value',
            'report_id': report.id,
        })
        
        wizard = self.wizard_model.create({
            'report_id': report.id,
        })
        
        wizard._onchange_report_id()
        
        self.assertEqual(len(wizard.param_lines), 1)
        self.assertEqual(wizard.param_lines[0].name, 'test_param')
        self.assertEqual(wizard.param_lines[0].value, 'default_value')
