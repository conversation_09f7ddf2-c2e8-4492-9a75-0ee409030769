import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class MssqlReportWizard(models.TransientModel):
    _name = 'mssql.report.wizard'
    _description = 'MSSQL Report Filter Wizard'

    report_id = fields.Many2one('mssql.report', string='Report', required=True)
    param_lines = fields.One2many('mssql.report.wizard.param', 'wizard_id', string='Parameters')

    @api.onchange('report_id')
    def _onchange_report_id(self):
        """Auto-populate parameters when report is selected"""
        if self.report_id:
            param_lines = []
            for param in self.report_id.parameter_ids:
                param_lines.append((0, 0, {
                    'name': param.name,
                    'param_type': param.param_type,
                    'value': param.default_value or '',
                }))
            self.param_lines = param_lines

    def action_show_report(self):
        """Execute report and show results"""
        if not self.report_id:
            raise UserError(_('Please select a report first.'))

        try:
            # Prepare parameters
            params = {}
            for line in self.param_lines:
                if line.value:
                    # Convert parameter value based on type
                    if line.param_type == 'int':
                        params[line.name] = int(line.value)
                    elif line.param_type in ('date', 'datetime'):
                        params[line.name] = line.value
                    else:
                        params[line.name] = line.value

            # Get report data
            result = self.report_id.get_report_data(params)
            data = result.get('data', [])
            columns = result.get('columns', [])

            # Clear old results for this user
            domain = [('create_uid', '=', self.env.uid)]
            self.env['mssql.report.result'].search(domain).unlink()

            # Create new results
            for row in data:
                self.env['mssql.report.result'].create({
                    'row_data': str(row),
                    'report_name': self.report_id.name,
                })

            return {
                'type': 'ir.actions.act_window',
                'name': _('MSSQL Report Results: %s') % self.report_id.name,
                'res_model': 'mssql.report.result',
                'view_mode': 'list',
                'target': 'main',
                'domain': [('create_uid', '=', self.env.uid)],
                'context': {
                    'default_report_name': self.report_id.name,
                }
            }

        except Exception as e:
            _logger.error(f'Error in report wizard: {str(e)}')
            raise UserError(_('Error executing report: %s') % str(e))

class MssqlReportWizardParam(models.TransientModel):
    _name = 'mssql.report.wizard.param'
    _description = 'MSSQL Report Wizard Param'

    wizard_id = fields.Many2one('mssql.report.wizard', string='Wizard', ondelete='cascade')
    name = fields.Char('Parameter', required=True)
    param_type = fields.Selection([
        ('char', 'String'),
        ('int', 'Integer'),
        ('date', 'Date'),
        ('datetime', 'Datetime'),
    ], string='Type', default='char')
    value = fields.Char('Value')
