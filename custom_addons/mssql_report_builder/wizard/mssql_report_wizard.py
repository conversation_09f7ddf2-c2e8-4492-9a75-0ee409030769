from odoo import models, fields, api, _
from odoo.exceptions import UserError

class MssqlReportWizard(models.TransientModel):
    _name = 'mssql.report.wizard'
    _description = 'MSSQL Report Filter Wizard'

    report_id = fields.Many2one('mssql.report', string='Report', required=True)
    param_lines = fields.One2many('mssql.report.wizard.param', 'wizard_id', string='Parameters')

    def action_show_report(self):
        params = {line.name: line.value for line in self.param_lines}
        data = self.report_id.get_report_data(params)
        # Xóa dữ liệu cũ
        self.env['mssql.report.result'].search([]).unlink()
        # Lưu dữ liệu mới
        for row in data:
            self.env['mssql.report.result'].create({'row_data': str(row)})
        return {
            'type': 'ir.actions.act_window',
            'name': '<PERSON><PERSON><PERSON> qu<PERSON> b<PERSON>o cáo MSSQL',
            'res_model': 'mssql.report.result',
            'view_mode': 'tree',
            'target': 'main',
        }

class MssqlReportWizardParam(models.TransientModel):
    _name = 'mssql.report.wizard.param'
    _description = 'MSSQL Report Wizard Param'

    wizard_id = fields.Many2one('mssql.report.wizard', string='Wizard')
    name = fields.Char('Parameter')
    value = fields.Char('Value')
