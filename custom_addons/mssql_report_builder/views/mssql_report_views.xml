<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_mssql_report_tree" model="ir.ui.view">
        <field name="name">mssql.report.tree</field>
        <field name="model">mssql.report</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="report_type"/>
                <field name="source_type"/>
                <field name="source_name"/>
            </list>
        </field>
    </record>

    <record id="view_mssql_report_form" model="ir.ui.view">
        <field name="name">mssql.report.form</field>
        <field name="model">mssql.report</field>
        <field name="arch" type="xml">
            <form string="MSSQL Report">
                <header>
                    <button name="action_run_report" type="object" string="Run Report" class="btn-primary"/>
                </header>
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="report_type"/>
                        <field name="source_type"/>
                        <field name="source_name"/>
                        <field name="active"/>
                    </group>
                    <group string="Parameters">
                        <field name="parameter_ids">
                            <list editable="bottom">
                                <field name="name"/>
                                <field name="param_type"/>
                                <field name="default_value"/>
                            </list>
                        </field>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_mssql_report" model="ir.actions.act_window">
        <field name="name">MSSQL Reports</field>
        <field name="res_model">mssql.report</field>
        <field name="view_mode">list,form</field>
    </record>

    <menuitem id="menu_mssql_report_root" name="MSSQL Reports" sequence="10"/>
    <menuitem id="menu_mssql_report" name="Report Definitions" parent="menu_mssql_report_root" action="action_mssql_report" sequence="10"/>
</odoo>
