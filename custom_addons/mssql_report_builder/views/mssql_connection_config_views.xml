<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- MSSQL Connection Config List View -->
    <record id="view_mssql_connection_config_tree" model="ir.ui.view">
        <field name="name">mssql.connection.config.tree</field>
        <field name="model">mssql.connection.config</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="host"/>
                <field name="database"/>
                <field name="username"/>
                <field name="is_default"/>
                <field name="active"/>
                <field name="last_test_result" widget="badge" 
                       decoration-success="last_test_result == 'success'"
                       decoration-danger="last_test_result == 'failed'"/>
                <field name="last_test_date"/>
                <button name="test_connection" type="object" string="Test" 
                        class="btn-primary" icon="fa-plug" 
                        title="Test Connection"/>
            </list>
        </field>
    </record>

    <!-- MSSQL Connection Config Form View -->
    <record id="view_mssql_connection_config_form" model="ir.ui.view">
        <field name="name">mssql.connection.config.form</field>
        <field name="model">mssql.connection.config</field>
        <field name="arch" type="xml">
            <form string="MSSQL Connection Configuration">
                <header>
                    <button name="test_connection" type="object" string="Test Connection" 
                            class="btn-primary" icon="fa-plug"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="test_connection" type="object" 
                                class="oe_stat_button" icon="fa-plug">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="last_test_result" widget="badge" 
                                           decoration-success="last_test_result == 'success'"
                                           decoration-danger="last_test_result == 'failed'"/>
                                </span>
                                <span class="o_stat_text">Connection</span>
                            </div>
                        </button>
                    </div>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Connection Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group string="Connection Settings">
                            <field name="host" placeholder="localhost or IP address"/>
                            <field name="port"/>
                            <field name="database" placeholder="Database name"/>
                            <field name="username" placeholder="SQL Server username"/>
                            <field name="password" password="True" placeholder="Password"/>
                        </group>
                        <group string="Options">
                            <field name="connection_method"/>
                            <field name="driver" invisible="connection_method != 'pyodbc'"/>
                            <field name="connection_timeout"/>
                            <field name="active"/>
                            <field name="is_default"/>
                            <field name="encrypt" invisible="connection_method != 'pyodbc'"/>
                            <field name="trust_server_certificate"
                                   invisible="connection_method != 'pyodbc' or not encrypt"/>
                        </group>
                    </group>

                    <group string="System Information">
                        <field name="available_drivers" readonly="1" widget="text"/>
                    </group>
                    
                    <group string="Test Results" invisible="not last_test_date">
                        <group>
                            <field name="last_test_date" readonly="1"/>
                            <field name="last_test_result" readonly="1" widget="badge" 
                                   decoration-success="last_test_result == 'success'"
                                   decoration-danger="last_test_result == 'failed'"/>
                        </group>
                        <group>
                            <field name="last_test_message" readonly="1"
                                   invisible="not last_test_message"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- MSSQL Connection Config Search View -->
    <record id="view_mssql_connection_config_search" model="ir.ui.view">
        <field name="name">mssql.connection.config.search</field>
        <field name="model">mssql.connection.config</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="host"/>
                <field name="database"/>
                <field name="username"/>
                <separator/>
                <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                <filter name="inactive" string="Inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter name="default" string="Default" domain="[('is_default', '=', True)]"/>
                <filter name="success" string="Last Test Success" domain="[('last_test_result', '=', 'success')]"/>
                <filter name="failed" string="Last Test Failed" domain="[('last_test_result', '=', 'failed')]"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_host" string="Host" context="{'group_by': 'host'}"/>
                    <filter name="group_by_database" string="Database" context="{'group_by': 'database'}"/>
                    <filter name="group_by_test_result" string="Test Result" context="{'group_by': 'last_test_result'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action for MSSQL Connection Config -->
    <record id="action_mssql_connection_config" model="ir.actions.act_window">
        <field name="name">MSSQL Connections</field>
        <field name="res_model">mssql.connection.config</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first MSSQL connection!
            </p>
            <p>
                Configure connections to your Microsoft SQL Server databases.
                You can test connections before saving and set a default connection for reports.
            </p>
        </field>
    </record>
</odoo>
