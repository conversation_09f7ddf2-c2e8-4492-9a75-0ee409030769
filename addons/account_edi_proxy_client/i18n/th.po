# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_proxy_client
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"A user already exists with theses credentials on our server. Please check "
"your information."
msgstr ""
"มีผู้ใช้ที่มีข้อมูลประจำตัวเหล่านี้อยู่แล้วบนเซิร์ฟเวอร์ของคุณ "
"โปรดตรวจสอบข้อมูลของคุณ"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "ผู้ใช้พร็อกซี EDI ของบัญชี"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_res_company__account_edi_proxy_client_ids
msgid "Account Edi Proxy Client"
msgstr "บัญชีพร็อกซีลูกค้า Edi"

#. module: account_edi_proxy_client
#: model_terms:ir.ui.view,arch_db:account_edi_proxy_client.view_form_account_edi_proxy_client_user
msgid "Account Journal"
msgstr "สมุดรายวันบัญชี"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__company_id
msgid "Company"
msgstr "บริษัท"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_certificate_key
msgid "Cryptographic Keys"
msgstr "คีย์การเข้ารหัส"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__demo
msgid "Demo mode"
msgstr "โหมดสาธิต"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: account_edi_proxy_client
#: model:ir.actions.act_window,name:account_edi_proxy_client.action_tree_account_edi_proxy_client_user
msgid "EDI Proxy User"
msgstr "ผู้ใช้พร็อกซี EDI"

#. module: account_edi_proxy_client
#: model:ir.ui.menu,name:account_edi_proxy_client.menu_account_proxy_client_user
msgid "EDI Proxy Users"
msgstr "ผู้ใช้พร็อกซี EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_mode
msgid "EDI operating mode"
msgstr "โหมดการทำงานของ EDI"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "Edi Identification"
msgstr "บัตรประจำตัวของ Edi"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id
msgid "ID"
msgstr "ไอดี"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id_client
msgid "Id Client"
msgstr "รหัสลูกค้า"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"Invalid signature for request. This might be due to another connection to odoo Access Point server. It can occur if you have duplicated your database. \n"
"\n"
"If you are not sure how to fix this, please contact our support."
msgstr ""

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_id
msgid "Private Key"
msgstr "รหัสส่วนตัว"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__prod
msgid "Production mode"
msgstr "โหมดการผลิต"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr "ประเภทพร็อกซี"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__refresh_token
msgid "Refresh Token"
msgstr "รีเฟรชโทเคน"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__test
msgid "Test mode"
msgstr "โหมดทดสอบ"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_id
msgid "The key to encrypt all the user's data"
msgstr "กุญแจสำคัญในการเข้ารหัสข้อมูลทั้งหมดของผู้ใช้"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr "รหัสเฉพาะที่ระบุผู้ใช้รายนี้ โดยทั่วไปคือ vat"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %(url)s. %(error_message)s"
msgstr ""
"URL ที่บริการนี้ร้องขอส่งคืนข้อผิดพลาด URL ที่พยายามติดต่อคือ %(url)s "
"%(error_message)s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s"
msgstr "URL ที่บริการนี้ขอส่งคืนข้อผิดพลาด URL ที่พยายามติดต่อคือ %s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
msgid ""
"The url that this service tried to contact does not exist. The url was “%s”"
msgstr "ไม่พบ URL ที่บริการนี้พยายามติดต่อ URL คือ “%s”"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_company_proxy
msgid "This company has an active user already created for this EDI type"
msgstr "บริษัทนี้มีผู้ใช้งานที่สร้างไว้แล้วสำหรับ EDI ประเภทนี้"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_edi_identification
msgid "This edi identification is already assigned to an active user"
msgstr "รหัส edi นี้ถูกกำหนดให้กับผู้ใช้ที่ใช้งานอยู่แล้ว"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_id_client
msgid "This id_client is already used on another user."
msgstr "id_client นี้ถูกใช้กับผู้ใช้อื่นแล้ว"
