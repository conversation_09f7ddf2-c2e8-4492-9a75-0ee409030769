<templates>

    <t t-name="account.FileUploadListRenderer" t-inherit="web.ListRenderer" t-inherit-mode="primary">
        <xpath expr="//div[@t-ref='root']" position="before">
            <UploadDropZone
                visible="dropzoneState.visible"
                hideZone="() => dropzoneState.visible = false"/>
        </xpath>
        <xpath expr="//div[@t-ref='root']" position="attributes">
            <attribute name="t-on-dragenter.stop.prevent">onDragStart</attribute>
            <attribute name="t-on-paste">onPaste</attribute>
        </xpath>
    </t>

</templates>
