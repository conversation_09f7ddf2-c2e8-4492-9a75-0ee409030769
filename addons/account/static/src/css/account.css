.openerp div.oe_account_help {
    background : #D6EBFF;
    width: 100%;
    padding: 10px;
    border: 3px solid #C1D4E6;
}

.openerp p.oe_account_font_help{
    text-align: left;
    font-weight: bold;
    margin: 0px;
    font-size: 14px;
}

.openerp p.oe_account_font_content{
    margin-left: 30px;
    font-size: 14px;
}

.openerp p.oe_account_font_title{
    margin-top: 7px;
    font-size: 15px;
    font-style: italic;
    color: grey;
}

.oe_invoice_outstanding_credits_debits {
    clear: both;
    float: right;
    min-width: 260px;
    /* The max-width ensures that the widget is not too wide in larger screens,
     but does not affect the width once the screen size decreases */
    max-width: 400px;
    margin-left: auto;
}

.oe_account_terms {
    flex: auto !important;
}

@media (max-width: 991.98px) {
    /* The purpose is to put the narration below the totals in the tab 'Invoice Lines'
    instead of above for the mobile view */
    .o_form_view .oe_invoice_lines_tab {
        display: flex;
        flex-direction: column-reverse;
    }

    .o_form_view .oe_invoice_lines_tab .oe_invoice_outstanding_credits_debits {
        min-width: initial;
        width: 50%;
    }
}

@media (max-width: 767.98px) {
    .o_form_view .oe_invoice_lines_tab .oe_invoice_outstanding_credits_debits {
        min-width: initial;
        width: 100%;
    }
}

.o_field_account_resequence_widget {
    width: 100%;
}

.o_field_account_json_checkboxes {
    div.form-check {
        display: inline-block;
    }

    i.fa {
        margin-left: 2px;
    }
}
