<templates>

    <t t-name="account.BillGuide">
        <div class="d-flex flex-row bill_guide_container mb-3">
            <div class="bill_guide_left flex-shrink-0 py-3">
                <div class="text-center">
                    <img class="img-fluid" src="/web/static/img/folder.svg"/>
                </div>
                <div class="text-center mt-2">
                    <span class="btn account_drag_drop_btn pe-none">Drag &amp; drop</span>
                    <div>
                        <span class="btn pe-none px-1 fw-normal">or</span>
                        <a class="btn btn-link px-0 fw-normal"
                            href="#"
                            type="object"
                            name="action_create_vendor_bill"
                            journal_type="purchase"
                            groups="account.group_account_invoice"
                            t-on-click="() => this.handleButtonClick('action_create_vendor_bill')"
                        >
                            try our sample
                        </a>
                    </div>
                </div>
            </div>
            <div class="separator_wrapper d-flex justify-content-center flex-shrink-1">
                <div class="word-separator d-flex flex-column align-items-center">
                    <div class="vertical-line border-start flex-grow-1 mt-2"/>
                    <div class="m-2">
                        or
                    </div>
                    <div class="vertical-line border-start flex-grow-1 mb-2"/>
                </div>
            </div>
            <div class="bill_guide_right flex-shrink-0 py-3">
                <div t-if="alias">
                    <div class="text-center">
                        <img class="img-fluid" src="/account/static/src/img/Bill.svg" alt="Email bills"/>
                    </div>
                    <div class="text-center mt-2">
                        <div class="">
                            <span class="btn pe-none px-1 fw-normal">Send a bill to</span>
                            <a class="btn btn-link px-0 fw-normal" t-attf-href="mailto:{{alias}}" t-out="alias"></a>
                        </div>
                        <div>
                            <span class="btn pe-none px-1 fw-normal">or</span>
                            <a href="#"
                                type="object"
                                class="btn btn-link px-0 fw-normal"
                                t-on-click="() => this.handleButtonClick('action_create_new')"
                                groups="account.group_account_invoice"
                            >
                                Create manually
                            </a>
                        </div>
                    </div>
                </div>
                <div t-else="">
                    <div class="text-center">
                        <img class="img-fluid"
                            src="/web/static/img/bill.svg"
                            alt="Create bill manually"/>
                    </div>
                    <div class="text-center mt-2">
                        <div class="">
                            <a href="#"
                                type="object"
                                class="o_invoice_new"
                                t-on-click="() => this.handleButtonClick('action_create_new')"
                                groups="account.group_account_invoice"
                            >
                                Create a bill manually
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
