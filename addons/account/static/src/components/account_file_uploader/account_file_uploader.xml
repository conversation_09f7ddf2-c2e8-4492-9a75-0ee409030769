<templates>

    <t t-name="account.AccountFileUploader" t-inherit="account.DocumentFileUploader" t-inherit-mode="primary">
        <xpath expr="//t[@t-slot='toggler']" position="replace">
            <t t-if="props.togglerTemplate" t-call="{{ props.togglerTemplate }}"/>
            <t t-else="" t-slot="toggler"/>
         </xpath>
    </t>

    <t t-name="account.AccountViewUploadButton">
        <AccountFileUploader>
            <t t-set-slot="toggler">
                <button type="button" class="btn btn-secondary o_button_upload_bill" data-hotkey="shift+i">
                    Upload
                </button>
            </t>
        </AccountFileUploader>
    </t>

    <t t-name="account.JournalUploadLink">
        <t groups="account.group_account_invoice">
            <a t-att-class="props.btnClass" href="#" t-out="props.linkText"/>
        </t>
    </t>

</templates>
