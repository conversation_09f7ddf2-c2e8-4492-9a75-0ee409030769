<?xml version="1.0" ?>
<odoo>

        <record id="print_pre_numbered_checks_view" model="ir.ui.view">
            <field name="name">Print Pre-numbered Checks</field>
            <field name="model">print.prenumbered.checks</field>
            <field name="arch" type="xml">
                <form string="Print Pre-numbered Checks">
                    <p>Please enter the number of the first pre-printed check that you are about to print on.</p>
                    <p>This will allow to save on payments the number of the corresponding check.</p>
                    <group>
                        <field name="next_check_number"/>
                    </group>
                    <footer>
                        <button name="print_checks" string="Print" type="object" class="oe_highlight" data-hotkey="q"/>
                        <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="x"/>
                    </footer>
                </form>
            </field>
        </record>

</odoo>
